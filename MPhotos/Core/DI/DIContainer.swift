//
//  DIContainer.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import Foundation
import UIKit
import Photos

/// 依赖注入容器 - 协议导向设计
final class DIContainer {
    
    // MARK: - 单例
    static let shared = DIContainer()
    
    // MARK: - 私有属性
    private var services: [String: Any] = [:]
    private var factories: [String: () -> Any] = [:]
    private let queue = DispatchQueue(label: "com.mphotos.di", attributes: .concurrent)
    
    private init() {
        registerDefaultServices()
    }
    
    // MARK: - 注册服务
    
    /// 注册单例服务
    func register<T>(_ type: T.Type, instance: T) {
        let key = String(describing: type)
        queue.async(flags: .barrier) {
            self.services[key] = instance
        }
    }
    
    /// 注册工厂方法
    func register<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        queue.async(flags: .barrier) {
            self.factories[key] = factory
        }
    }
    
    /// 注册协议实现
    func register<T>(_ protocol: T.Type, implementation: T) {
        register(`protocol`, instance: implementation)
    }
    
    // MARK: - 解析服务
    
    /// 解析服务实例
    func resolve<T>(_ type: T.Type) -> T? {
        let key = String(describing: type)
        
        return queue.sync {
            // 首先检查已注册的实例
            if let instance = services[key] as? T {
                return instance
            }
            
            // 然后检查工厂方法
            if let factory = factories[key] {
                let instance = factory() as? T
                return instance
            }
            
            return nil
        }
    }
    
    /// 强制解析服务（如果失败会崩溃）
    func forceResolve<T>(_ type: T.Type) -> T {
        guard let service = resolve(type) else {
            fatalError("无法解析服务: \(String(describing: type))")
        }
        return service
    }
    
    /// 解析可选服务
    func resolveOptional<T>(_ type: T.Type) -> T? {
        return resolve(type)
    }
    
    // MARK: - 服务管理
    
    /// 移除服务
    func unregister<T>(_ type: T.Type) {
        let key = String(describing: type)
        queue.async(flags: .barrier) {
            self.services.removeValue(forKey: key)
            self.factories.removeValue(forKey: key)
        }
    }
    
    /// 清空所有服务
    func clear() {
        queue.async(flags: .barrier) {
            self.services.removeAll()
            self.factories.removeAll()
        }
    }
    
    /// 检查服务是否已注册
    func isRegistered<T>(_ type: T.Type) -> Bool {
        let key = String(describing: type)
        return queue.sync {
            return services[key] != nil || factories[key] != nil
        }
    }
    
    /// 获取所有已注册的服务类型
    func getRegisteredServices() -> [String] {
        return queue.sync {
            return Array(Set(services.keys).union(Set(factories.keys)))
        }
    }
    
    // MARK: - 默认服务注册
    
    private func registerDefaultServices() {
        // 注册缓存管理器
        register(CacheManaging.self, implementation: LightweightCacheManager.shared)

        // 注册内存监控器
        register(MemoryMonitor.self, instance: MemoryMonitor())

        // 注册性能监控器
        register(PerformanceMonitor.self, instance: PerformanceMonitor.shared)

        // 注册媒体管理器工厂
        register(MediaManaging.self) {
            return MediaManager()
        }

        // 注册照片库服务
        register(SimplifiedPhotoLibraryService.self, instance: SimplifiedPhotoLibraryService.shared)
    }
}

// MARK: - 便利扩展

extension DIContainer {
    
    /// 便利方法：注册并立即返回实例
    @discardableResult
    func registerAndResolve<T>(_ type: T.Type, instance: T) -> T {
        register(type, instance: instance)
        return instance
    }
    
    /// 便利方法：注册工厂并立即创建实例
    @discardableResult
    func registerAndCreate<T>(_ type: T.Type, factory: @escaping () -> T) -> T {
        register(type, factory: factory)
        return factory()
    }
}

// MARK: - 属性包装器

/// 依赖注入属性包装器
@propertyWrapper
struct Injected<T> {
    private let type: T.Type
    
    init(_ type: T.Type) {
        self.type = type
    }
    
    var wrappedValue: T {
        guard let service = DIContainer.shared.resolve(type) else {
            fatalError("无法注入依赖: \(String(describing: type))")
        }
        return service
    }
}

/// 可选依赖注入属性包装器
@propertyWrapper
struct OptionalInjected<T> {
    private let type: T.Type
    
    init(_ type: T.Type) {
        self.type = type
    }
    
    var wrappedValue: T? {
        return DIContainer.shared.resolve(type)
    }
}

// MARK: - 协议定义

/// 媒体管理协议
protocol MediaManaging {
    func loadThumbnail(for asset: PHAsset, targetSize: CGSize, completion: @escaping (Result<UIImage, PhotosError>) -> Void)
    func loadFullImage(for asset: PHAsset, completion: @escaping (Result<UIImage, PhotosError>) -> Void)
    func cancelRequest(for asset: PHAsset)
    func preloadImages(for assets: [PHAsset], targetSize: CGSize)
}

// MARK: - 扩展实现
// LightweightCacheManager 的 CacheManaging 协议实现在其自己的文件中

// MARK: - 使用示例

/*
 使用示例：
 
 // 1. 注册服务
 DIContainer.shared.register(MyService.self, instance: MyService())
 
 // 2. 解析服务
 let service = DIContainer.shared.resolve(MyService.self)
 
 // 3. 使用属性包装器
 class MyViewController {
     @Injected(CacheManaging.self) var cacheManager
     @OptionalInjected(MyOptionalService.self) var optionalService
 }
 
 // 4. 注册工厂方法
 DIContainer.shared.register(MyService.self) {
     return MyService(config: MyConfig())
 }
 */
