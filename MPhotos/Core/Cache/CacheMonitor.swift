//
//  CacheMonitor.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit
import Foundation

/// 缓存监控器 - 监控缓存性能和内存使用情况
final class CacheMonitor {
    
    // MARK: - 单例
    static let shared = CacheMonitor()
    
    // MARK: - 私有属性
    private var isMonitoring = false
    private var monitoringTimer: Timer?
    private let monitoringQueue = DispatchQueue(label: "com.mphotos.cache.monitor", qos: .utility)
    
    // MARK: - 监控数据
    private var cacheHitRate: Double = 0.0
    private var memoryUsage: Int64 = 0
    private var diskUsage: Int64 = 0
    private var lastUpdateTime: Date = Date()
    
    // MARK: - 回调
    var onCacheStatsUpdate: ((CacheStatistics) -> Void)?
    var onMemoryWarning: (() -> Void)?
    
    // MARK: - 初始化
    private init() {
        setupMemoryWarningNotification()
    }
    
    deinit {
        stopMonitoring()
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 公共接口
    
    /// 开始监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        
        // 每5秒更新一次缓存统计
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            self?.updateCacheStatistics()
        }
        
        print("📊 CacheMonitor 开始监控")
    }
    
    /// 停止监控
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        print("📊 CacheMonitor 停止监控")
    }
    
    /// 获取当前缓存统计
    func getCurrentStatistics() -> CacheStatistics {
        if let cacheManager = DIContainer.shared.resolve(CacheManaging.self) as? LightweightCacheManager {
            return cacheManager.getCacheStatistics()
        }
        
        return CacheStatistics(
            hitCount: 0,
            missCount: 0,
            memoryUsage: memoryUsage,
            diskUsage: diskUsage
        )
    }
    
    /// 获取缓存命中率
    func getCacheHitRate() -> Double {
        return cacheHitRate
    }
    
    /// 获取内存使用情况
    func getMemoryUsage() -> Int64 {
        return memoryUsage
    }
    
    /// 获取磁盘使用情况
    func getDiskUsage() -> Int64 {
        return diskUsage
    }
    
    /// 获取格式化的统计信息
    func getFormattedStatistics() -> String {
        let stats = getCurrentStatistics()
        let hitRate = stats.hitCount + stats.missCount > 0 ? 
            Double(stats.hitCount) / Double(stats.hitCount + stats.missCount) * 100 : 0
        
        return """
        缓存统计:
        - 命中率: \(String(format: "%.1f", hitRate))%
        - 命中次数: \(stats.hitCount)
        - 未命中次数: \(stats.missCount)
        - 内存使用: \(ByteCountFormatter.string(fromByteCount: stats.memoryUsage, countStyle: .memory))
        - 磁盘使用: \(ByteCountFormatter.string(fromByteCount: stats.diskUsage, countStyle: .file))
        """
    }
    
    // MARK: - 私有方法
    
    /// 更新缓存统计
    private func updateCacheStatistics() {
        monitoringQueue.async { [weak self] in
            guard let self = self else { return }
            
            let stats = self.getCurrentStatistics()
            self.memoryUsage = stats.memoryUsage
            self.diskUsage = stats.diskUsage
            
            // 计算命中率
            let totalRequests = stats.hitCount + stats.missCount
            self.cacheHitRate = totalRequests > 0 ? Double(stats.hitCount) / Double(totalRequests) : 0.0
            
            self.lastUpdateTime = Date()
            
            // 通知回调
            DispatchQueue.main.async {
                self.onCacheStatsUpdate?(stats)
            }
            
            // 检查内存使用情况
            self.checkMemoryUsage(stats.memoryUsage)
        }
    }
    
    /// 检查内存使用情况
    private func checkMemoryUsage(_ usage: Int64) {
        // 如果内存使用超过100MB，触发警告
        let warningThreshold: Int64 = 100 * 1024 * 1024
        
        if usage > warningThreshold {
            DispatchQueue.main.async { [weak self] in
                self?.onMemoryWarning?()
            }
        }
    }
    
    /// 设置内存警告通知
    private func setupMemoryWarningNotification() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    /// 处理内存警告
    @objc private func handleMemoryWarning() {
        print("⚠️ 收到系统内存警告")
        onMemoryWarning?()
    }
}

// MARK: - 扩展：调试功能

extension CacheMonitor {
    
    /// 打印当前统计信息
    func printStatistics() {
        print("📊 \(getFormattedStatistics())")
    }
    
    /// 重置统计信息
    func resetStatistics() {
        if let cacheManager = DIContainer.shared.resolve(CacheManaging.self) as? LightweightCacheManager {
            // 重置缓存管理器的统计信息
            cacheManager.resetStatistics()
        }
        
        cacheHitRate = 0.0
        memoryUsage = 0
        diskUsage = 0
        lastUpdateTime = Date()
        
        print("📊 缓存统计信息已重置")
    }
}
