//
//  ProgressiveLoadingManager.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit
import Photos

/// 渐进式加载管理器 - 实现两阶段图片加载策略
final class ProgressiveLoadingManager {
    
    // MARK: - 依赖
    private let imageManager: PHImageManager
    private let thumbnailCache: NSCache<NSString, UIImage>
    
    // MARK: - 私有属性
    private var activeRequests: [String: PHImageRequestID] = [:]
    private let requestQueue = DispatchQueue(label: "com.mphotos.progressive", attributes: .concurrent)
    
    // MARK: - 初始化
    
    init(imageManager: PHImageManager, thumbnailCache: NSCache<NSString, UIImage>) {
        self.imageManager = imageManager
        self.thumbnailCache = thumbnailCache
    }
    
    // MARK: - 公共接口
    
    /// 渐进式加载缩略图
    /// - Parameters:
    ///   - photo: 照片模型
    ///   - targetSize: 目标尺寸
    ///   - completion: 完成回调（可能被调用多次）
    func loadThumbnailProgressive(for photo: PhotoModel, targetSize: CGSize, completion: @escaping (Result<UIImage, Error>, Bool) -> Void) {
        let photoId = photo.id
        let asset = photo.asset
        
        // 取消之前的请求
        cancelLoading(for: photoId)
        
        // 生成缓存键
        let cacheKey = generateCacheKey(for: asset, size: targetSize)
        
        // 检查缓存
        if let cachedImage = thumbnailCache.object(forKey: cacheKey as NSString) {
            completion(.success(cachedImage), true)
            return
        }
        
        // 第一阶段：快速加载低分辨率版本
        let lowResSize = CGSize(width: targetSize.width * 0.5, height: targetSize.height * 0.5)
        let lowResOptions = PHImageRequestOptions()
        lowResOptions.deliveryMode = .fastFormat
        lowResOptions.resizeMode = .fast
        lowResOptions.isNetworkAccessAllowed = false
        lowResOptions.isSynchronous = false
        
        let lowResRequestID = imageManager.requestImage(
            for: asset,
            targetSize: lowResSize,
            contentMode: .aspectFill,
            options: lowResOptions
        ) { [weak self] image, info in
            guard let self = self, let image = image else { return }
            
            // 返回低分辨率版本
            completion(.success(image), false)
            
            // 第二阶段：后台加载高分辨率版本
            self.loadHighResolution(for: asset, targetSize: targetSize, cacheKey: cacheKey, completion: completion)
        }
        
        // 记录请求ID
        requestQueue.async(flags: .barrier) {
            self.activeRequests[photoId] = lowResRequestID
        }
    }
    
    /// 取消指定照片的加载
    /// - Parameter photoId: 照片ID
    func cancelLoading(for photoId: String) {
        requestQueue.async(flags: .barrier) { [weak self] in
            guard let self = self,
                  let requestID = self.activeRequests[photoId] else { return }
            
            self.imageManager.cancelImageRequest(requestID)
            self.activeRequests.removeValue(forKey: photoId)
        }
    }
    
    /// 取消所有活跃请求
    func cancelAllRequests() {
        requestQueue.async(flags: .barrier) { [weak self] in
            guard let self = self else { return }
            
            for requestID in self.activeRequests.values {
                self.imageManager.cancelImageRequest(requestID)
            }
            self.activeRequests.removeAll()
        }
    }
    
    /// 获取活跃请求数量
    func getActiveRequestCount() -> Int {
        return requestQueue.sync {
            return activeRequests.count
        }
    }
    
    // MARK: - 私有方法
    
    /// 加载高分辨率版本
    private func loadHighResolution(for asset: PHAsset, targetSize: CGSize, cacheKey: String, completion: @escaping (Result<UIImage, Error>, Bool) -> Void) {
        let highResOptions = PHImageRequestOptions()
        highResOptions.deliveryMode = .highQualityFormat
        highResOptions.resizeMode = .exact
        highResOptions.isNetworkAccessAllowed = true
        highResOptions.isSynchronous = false
        
        let highResRequestID = imageManager.requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: highResOptions
        ) { [weak self] image, info in
            guard let self = self, let image = image else { return }
            
            // 缓存高分辨率版本
            self.thumbnailCache.setObject(image, forKey: cacheKey as NSString)
            
            // 返回高分辨率版本
            completion(.success(image), true)
        }
        
        // 更新请求ID
        requestQueue.async(flags: .barrier) {
            self.activeRequests[asset.localIdentifier] = highResRequestID
        }
    }
    
    /// 生成缓存键
    private func generateCacheKey(for asset: PHAsset, size: CGSize) -> String {
        return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))"
    }
}

// MARK: - 错误类型

enum ProgressiveLoadingError: LocalizedError {
    case assetNotFound
    case loadingFailed
    case cancelled
    
    var errorDescription: String? {
        switch self {
        case .assetNotFound:
            return "找不到指定的资源"
        case .loadingFailed:
            return "图片加载失败"
        case .cancelled:
            return "加载已取消"
        }
    }
}
